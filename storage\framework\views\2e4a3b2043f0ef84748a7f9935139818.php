<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create Penalty')); ?>

<?php $__env->startSection('css_links'); ?>
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/typography.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/katex.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/quill/editor.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }
        .penalty-form .form-label {
            font-weight: 600;
        }
        .penalty-form .required::after {
            content: " *";
            color: red;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create Penalty')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.penalty.index')); ?>"><?php echo e(__('All Penalties')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Create Penalty')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('Create New Penalty')); ?></h5>
            </div>
            <div class="card-body penalty-form">
                <form action="<?php echo e(route('administration.penalty.store')); ?>" method="POST" enctype="multipart/form-data" id="penaltyForm">
                    <?php echo csrf_field(); ?>

                    <div class="row">
                        <!-- Employee Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label required"><?php echo e(__('Select Employee')); ?></label>
                            <select class="form-select select2" id="user_id" name="user_id" required>
                                <option value=""><?php echo e(__('Choose Employee...')); ?></option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($user->id); ?>" <?php echo e(old('user_id') == $user->id ? 'selected' : ''); ?>>
                                        <?php echo e($user->name); ?> (<?php echo e($user->employee->alias_name ?? 'N/A'); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Attendance Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="attendance_id" class="form-label required"><?php echo e(__('Select Attendance')); ?></label>
                            <select class="form-select select2" id="attendance_id" name="attendance_id" required disabled>
                                <option value=""><?php echo e(__('First select an employee...')); ?></option>
                            </select>
                            <?php $__errorArgs = ['attendance_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Penalty Type -->
                        <div class="col-md-5 mb-3">
                            <label for="type" class="form-label required"><?php echo e(__('Penalty Type')); ?></label>
                            <select class="form-select select2" id="type" name="type" required>
                                <option value=""><?php echo e(__('Choose Penalty Type...')); ?></option>
                                <?php $__currentLoopData = $penaltyTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($type); ?>" <?php echo e(old('type') == $type ? 'selected' : ''); ?>>
                                        <?php echo e($type); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Penalty Time -->
                        <div class="mb-3 col-md-3">
                            <label for="total_time" class="form-label required"><?php echo e(__('Penalty Time (Minutes)')); ?></label>
                            <input type="number" class="form-control" id="total_time" name="total_time" value="<?php echo e(old('total_time')); ?>" min="1" max="480" step="1" required>
                            <div class="form-text"><?php echo e(__('Enter penalty time in minutes (1-480)')); ?></div>
                            <?php $__errorArgs = ['total_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-4" id="fileInputContainer">
                            <label for="files[]" class="form-label"><?php echo e(__('Prescription/Proof Files')); ?></label>
                            <input type="file" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" id="files[]" name="files[]" value="<?php echo e(old('files[]')); ?>" placeholder="<?php echo e(__('Prescription/Proof Files')); ?>" class="form-control <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple/>
                            <?php $__errorArgs = ['files[]'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle mr-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="mb-3 col-md-12">
                        <label class="form-label"><?php echo e(__('Penalty Reason')); ?> <strong class="text-danger">*</strong></label>
                        <div name="reason" id="full-editor"><?php echo old('reason'); ?></div>
                        <textarea class="d-none" name="reason" id="reason-input" placeholder="<?php echo e(__('Provide detailed reason for the penalty...')); ?>"><?php echo e(old('reason')); ?></textarea>
                        <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <b class="text-danger"><?php echo e($message); ?></b>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <button type="submit" class="btn btn-danger">
                            <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Submit Penalty')); ?>

                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    <!-- Vendors JS -->
    <script src="<?php echo e(asset('assets/vendor/libs/quill/katex.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/quill/quill.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2();

            // Handle employee selection change
            $('#user_id').on('change', function() {
                const userId = $(this).val();
                const attendanceSelect = $('#attendance_id');

                if (userId) {
                    // Enable attendance dropdown and show loading
                    attendanceSelect.prop('disabled', false);
                    attendanceSelect.html('<option value="">Loading...</option>');

                    // Fetch attendances for selected user
                    $.ajax({
                        url: '<?php echo e(route('administration.penalty.attendances')); ?>',
                        method: 'GET',
                        data: { user_id: userId },
                        success: function(data) {
                            console.log('Attendance data received:', data);
                            let options = '<option value="">Choose Attendance...</option>';
                            if (data && data.length > 0) {
                                data.forEach(function(attendance) {
                                    options += `<option value="${attendance.id}">${attendance.text}</option>`;
                                });
                            } else {
                                options += '<option value="">No attendance records found for today</option>';
                            }
                            attendanceSelect.html(options);
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX Error:', xhr.responseText);
                            attendanceSelect.html('<option value="">Error loading attendances</option>');
                        }
                    });
                } else {
                    // Reset attendance dropdown
                    attendanceSelect.prop('disabled', true);
                    attendanceSelect.html('<option value="">First select an employee...</option>');
                }
            });
        });
    </script>



    <script>
        $(document).ready(function () {
            var fullToolbar = [
                [{ font: [] }, { size: [] }],
                ["bold", "italic", "underline", "strike"],
                [{ color: [] }, { background: [] }],
                ["link"],
                [{ header: "1" }, { header: "2" }, "blockquote", "code-block"],
                [{ list: "ordered" }, { list: "bullet" }, { indent: "-1" }, { indent: "+1" }],
            ];

            var fullEditor = new Quill("#full-editor", {
                bounds: "#full-editor",
                placeholder: "Ex: The employee did not follow the dress code.",
                modules: {
                    formula: true,
                    toolbar: fullToolbar,
                },
                theme: "snow",
            });

            // Set the editor content to the old reason if validation fails
            <?php if(old('reason')): ?>
                fullEditor.root.innerHTML = <?php echo json_encode(old('reason')); ?>;
            <?php endif; ?>

            $('#penaltyForm').on('submit', function() {
                $('#reason-input').val(fullEditor.root.innerHTML);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/penalty/create.blade.php ENDPATH**/ ?>