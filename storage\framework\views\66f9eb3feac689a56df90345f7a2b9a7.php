<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Penalty Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- Lightbox CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/lightbox2/css/lightbox.min.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .penalty-details .info-label {
            font-weight: 600;
            color: #566a7f;
        }
        .penalty-details .info-value {
            color: #32394e;
        }
        .file-item {
            border: 1px solid #e7eaf3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .file-item:hover {
            border-color: #696cff;
            box-shadow: 0 2px 8px rgba(105, 108, 255, 0.1);
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Penalty Details')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.penalty.index')); ?>"><?php echo e(__('All Penalties')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Penalty Details')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('Penalty Information')); ?></h5>
                <a href="<?php echo e(route('administration.penalty.index')); ?>" class="btn btn-outline-secondary">
                    <i class="ti ti-arrow-left me-1"></i><?php echo e(__('Back to List')); ?>

                </a>
            </div>
            <div class="card-body penalty-details">
                <div class="row">
                    <!-- Employee Information -->
                    <div class="col-md-6 mb-4">
                        <h6 class="text-primary mb-3"><?php echo e(__('Employee Information')); ?></h6>
                        <div class="d-flex align-items-center mb-3">
                            <?php if($penalty->user->media->isNotEmpty()): ?>
                                <img src="<?php echo e($penalty->user->media->first()->getUrl('thumb_color')); ?>"
                                     alt="<?php echo e($penalty->user->name); ?>"
                                     class="rounded-circle me-3"
                                     width="60" height="60">
                            <?php else: ?>
                                <div class="avatar avatar-lg me-3">
                                    <span class="avatar-initial rounded-circle bg-label-primary fs-4">
                                        <?php echo e(substr($penalty->user->name, 0, 1)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo e($penalty->user->name); ?></h6>
                                <small class="text-muted"><?php echo e($penalty->user->employee->alias_name ?? 'N/A'); ?></small>
                                <br>
                                <small class="text-muted"><?php echo e($penalty->user->employee->official_email ?? 'N/A'); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- Penalty Information -->
                    <div class="col-md-6 mb-4">
                        <h6 class="text-primary mb-3"><?php echo e(__('Penalty Details')); ?></h6>
                        <div class="row">
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Type:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="badge bg-label-warning"><?php echo e($penalty->type); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Penalty Time:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-value fw-medium"><?php echo e($penalty->total_time_formatted); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Created:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-value"><?php echo e($penalty->created_at->format('M d, Y H:i')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Attendance Information -->
                    <div class="col-md-6 mb-4">
                        <h6 class="text-primary mb-3"><?php echo e(__('Related Attendance')); ?></h6>
                        <div class="row">
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Date:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-value"><?php echo e($penalty->attendance->clock_in_date ?? 'N/A'); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Clock In:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-value"><?php echo e($penalty->attendance->clock_in ? show_time($penalty->attendance->clock_in) : 'N/A'); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Clock Out:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-value"><?php echo e($penalty->attendance->clock_out ? show_time($penalty->attendance->clock_out) : 'Ongoing'); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="info-label"><?php echo e(__('Type:')); ?></span>
                            </div>
                            <div class="col-6 mb-2">
                                <span class="badge bg-label-info"><?php echo e($penalty->attendance->type ?? 'N/A'); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Creator Information -->
                    <div class="col-md-6 mb-4">
                        <h6 class="text-primary mb-3"><?php echo e(__('Created By')); ?></h6>
                        <div class="d-flex align-items-center">
                            <?php if($penalty->creator->media->isNotEmpty()): ?>
                                <img src="<?php echo e($penalty->creator->media->first()->getUrl('thumb_color')); ?>"
                                     alt="<?php echo e($penalty->creator->name); ?>"
                                     class="rounded-circle me-3"
                                     width="50" height="50">
                            <?php else: ?>
                                <div class="avatar me-3">
                                    <span class="avatar-initial rounded-circle bg-label-secondary">
                                        <?php echo e(substr($penalty->creator->name, 0, 1)); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h6 class="mb-1"><?php echo e($penalty->creator->name); ?></h6>
                                <small class="text-muted"><?php echo e($penalty->creator->employee->alias_name ?? 'N/A'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reason -->
                <div class="mb-4">
                    <h6 class="text-primary mb-3"><?php echo e(__('Reason for Penalty')); ?></h6>
                    <div class="rounded">
                        <p class="mb-0"><?php echo $penalty->reason; ?></p>
                    </div>
                </div>

                <!-- Penalty Proof Files -->
                <?php if($penalty->files->count() > 0): ?>
                    <div class="mb-4">
                        <h6 class="text-primary mb-3"><?php echo e(__('Penalty Proof Files')); ?></h6>
                        <div class="row">
                            <?php $__currentLoopData = $penalty->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="file-item">
                                        <?php if(in_array($file->mime_type, ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])): ?>
                                            <a href="<?php echo e(asset('storage/' . $file->file_path)); ?>" data-lightbox="penalty-files" data-title="<?php echo e($file->original_name); ?>">
                                                <img src="<?php echo e(asset('storage/' . $file->file_path)); ?>"
                                                     alt="<?php echo e($file->original_name); ?>"
                                                     class="img-fluid rounded mb-2"
                                                     style="max-height: 150px; width: 100%; object-fit: cover;">
                                            </a>
                                        <?php else: ?>
                                            <div class="text-center mb-2">
                                                <i class="ti ti-file-text display-4 text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <h6 class="mb-1"><?php echo e($file->original_name); ?></h6>
                                        <small class="text-muted"><?php echo e(number_format($file->file_size / 1024, 2)); ?> KB</small>
                                        <div class="mt-2">
                                            <a href="<?php echo e(route('administration.file.download', $file)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-download me-1"></i><?php echo e(__('Download')); ?>

                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor_js'); ?>
    
    <!-- Lightbox JS -->
    <script src="<?php echo e(asset('assets/vendor/libs/lightbox2/js/lightbox.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_js'); ?>
    
    <script>
        $(document).ready(function() {
            // Initialize lightbox
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/penalty/show.blade.php ENDPATH**/ ?>